<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <!-- SEO Meta Tags -->
  <meta name="description"
    content="Senior Flutter Developer & Mobile Architect with 6+ years of experience in cross-platform development. Explore my projects, skills, and get in touch.">
  <meta name="author" content="Saksham Srivastava">
  <meta name="keywords"
    content="Flutter Developer, Mobile Architect, Cross-Platform Development, Mobile App Development, Senior Developer, Portfolio">

  <!-- Open Graph Meta Tags for Social Sharing (Used by LinkedIn and other platforms) -->
  <meta property="og:title" content="Saksham Srivastava | Senior Flutter Developer & Mobile Architect">
  <meta property="og:description"
    content="Senior Flutter Developer & Mobile Architect with 6+ years of experience in cross-platform development. Explore my projects, skills, and get in touch.">
  <meta property="og:image" content="https://saksham-portfolio-ba828.web.app/assets/images/social_preview.png">
  <meta property="og:image:width" content="1306">
  <meta property="og:image:height" content="683">
  <meta property="og:image:alt" content="Saksham Srivastava Portfolio Preview">
  <meta property="og:url" content="https://saksham-portfolio-ba828.web.app">
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="Saksham Srivastava Portfolio">
  <meta property="og:locale" content="en_US">

  <!-- LinkedIn Specific Meta Tags (LinkedIn primarily uses Open Graph tags above) -->
  <meta name="author" content="Saksham Srivastava">
  <meta name="linkedin:author" content="https://linkedin.com/in/sakshamsri/">

  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Saksham Srivastava | Senior Flutter Developer & Mobile Architect">
  <meta name="twitter:description"
    content="Senior Flutter Developer & Mobile Architect with 6+ years of experience in cross-platform development. Explore my projects, skills, and get in touch.">
  <meta name="twitter:image" content="https://saksham-portfolio-ba828.web.app/assets/images/social_preview.png">
  <meta name="twitter:creator" content="@sakshamsri4">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">

  <!-- Favicon and App Icons -->
  <link rel="icon" type="image/svg+xml" href="assets/images/logo.svg">
  <link rel="apple-touch-icon" href="assets/images/logo.svg">

  <!-- Canonical URL -->
  <link rel="canonical" href="https://saksham-portfolio-ba828.web.app">

  <!-- Web App Title -->
  <meta name="apple-mobile-web-app-title" content="Saksham Portfolio">
  <meta name="application-name" content="Saksham Portfolio">

  <!-- Title -->
  <title>Saksham Srivastava | Senior Flutter Developer & Mobile Architect</title>

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&display=swap"
    rel="stylesheet">

  <!-- Disable Firebase auto-injection completely to prevent platform channel conflicts -->
  <script>
    // Prevent Firebase auto-injection of all plugins to avoid dual initialization
    window.flutterfire_ignore_scripts = true;

    // Also disable automatic Firebase initialization
    window.firebase_core_auto_init = false;
    window.firebase_analytics_auto_init = false;
  </script>

  <!-- Firebase JavaScript SDK -->
  <script type="module">
    // Import Firebase SDK modules
    import { initializeApp } from 'https://www.gstatic.com/firebasejs/11.9.1/firebase-app.js';
    import { getAnalytics } from 'https://www.gstatic.com/firebasejs/11.9.1/firebase-analytics.js';

    // Firebase configuration
    const firebaseConfig = {
      apiKey: 'AIzaSyDmsKfdPCsOyE4V9b9rh_7M4GIoQh4zcsM',
      appId: '1:51439261225:web:bb97fff3613e72ef96ae38',
      messagingSenderId: '51439261225',
      projectId: 'saksham-portfolio-ba828',
      authDomain: 'saksham-portfolio-ba828.firebaseapp.com',
      databaseURL: 'https://saksham-portfolio-ba828-default-rtdb.firebaseio.com',
      storageBucket: 'saksham-portfolio-ba828.firebasestorage.app',
      measurementId: 'G-VVVFQJL1WD',
    };

    // Initialize Firebase with error handling
    try {
      const app = initializeApp(firebaseConfig);
      const analytics = getAnalytics(app);

      // Make Firebase available globally for Flutter with better error handling
      window.firebase_core = {
        initializeApp: () => app,
        app: () => app
      };
      window.firebase_analytics = {
        getAnalytics: () => analytics,
        analytics: analytics
      };
      window.firebaseConfig = firebaseConfig;
      window.firebaseApp = app;
      window.firebaseAnalytics = analytics;

      console.log('Firebase JavaScript SDK initialized successfully');
    } catch (error) {
      console.warn('Firebase JavaScript SDK initialization failed:', error);
      // Provide fallback objects to prevent platform channel errors
      window.firebase_core = {
        initializeApp: () => null,
        app: () => null
      };
      window.firebase_analytics = {
        getAnalytics: () => null,
        analytics: null
      };
    }
  </script>

</head>

<body>
  <!-- Static content for social media crawlers -->
  <div id="social-media-content" style="display: none;">
    <h1>Saksham Srivastava | Senior Flutter Developer & Mobile Architect</h1>
    <p>Senior Flutter Developer & Mobile Architect with 6+ years of experience in cross-platform development. Explore my
      projects, skills, and get in touch.</p>
    <img src="https://saksham-portfolio-ba828.web.app/assets/images/social_preview.png"
      alt="Saksham Srivastava Portfolio Preview" width="1306" height="683">
    <p>Skills: Flutter, Dart, Firebase, GetX, BLoC, Provider, REST APIs, GraphQL, CI/CD, Mobile Architecture</p>
    <p>Visit my portfolio to see my projects and get in touch!</p>
  </div>

  <div id="loading">
    <style>
      body {
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }

      #loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        width: 100vw;
      }

      #loading svg {
        width: 100px;
        height: 100px;
        animation: 1s ease-in-out 0s infinite alternate breathe;
        opacity: .9;
        transition: opacity .4s;
      }

      #loading.main_done svg {
        opacity: 1;
      }

      #loading.init_done svg {
        animation: .33s ease-in-out 0s 1 forwards zooooom;
        opacity: .05;
      }

      @keyframes breathe {
        from {
          transform: scale(1)
        }

        to {
          transform: scale(0.95)
        }
      }

      @keyframes zooooom {
        from {
          transform: scale(1)
        }

        to {
          transform: scale(10)
        }
      }
    </style>
    <!-- Simple text-based logo with web-safe font -->
    <div class="splash-logo">
      <style>
        .splash-logo {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 120px;
          height: 120px;
          border-radius: 50%;
          background-color: #675AF2;
          /* Using system sans-serif font - stable across all platforms */
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
          font-weight: 700;
          font-size: 60px;
          color: white;
          animation: 1s ease-in-out 0s infinite alternate breathe;
          opacity: .9;
          transition: opacity .4s;
        }

        #loading.main_done .splash-logo {
          opacity: 1;
        }

        #loading.init_done .splash-logo {
          animation: .33s ease-in-out 0s 1 forwards zooooom;
          opacity: .05;
        }
      </style>
      <span>S</span>
    </div>
  </div>

  <!-- Flutter Bootstrap Script -->
  <script src="flutter_bootstrap.js" async></script>

  <!-- Custom loading animation and initialization -->
  <script>
    window.addEventListener('load', function () {
      var loading = document.querySelector('#loading');

      // Wait for Flutter loader to be available
      function waitForFlutter() {
        if (window._flutter && window._flutter.loader) {
          _flutter.loader.load({
            serviceWorker: {
              serviceWorkerVersion: null,
            },
            onEntrypointLoaded: async function (engineInitializer) {
              loading.classList.add('main_done');
              let appRunner = await engineInitializer.initializeEngine();
              loading.classList.add('init_done');
              await appRunner.runApp();
            }
          });
        } else {
          // Retry after a short delay
          setTimeout(waitForFlutter, 50);
        }
      }

      waitForFlutter();
    });
  </script>
</body>